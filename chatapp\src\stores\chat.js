import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { chatAPI } from '../utils/api'

export const useChatStore = defineStore('chat', () => {
  const messages = ref([])
  const onlineUsers = ref([])
  const lastMessageId = ref(0)
  const isScrolledToBottom = ref(true)
  
  const sortedMessages = computed(() => {
    return messages.value.sort((a, b) => new Date(a.created_at) - new Date(b.created_at))
  })
  
  const fetchMessages = async () => {
    try {
      const response = await chatAPI.getMessages(lastMessageId.value)
      if (response.data.messages && response.data.messages.length > 0) {
        response.data.messages.forEach(message => {
          if (!messages.value.find(m => m.id === message.id)) {
            messages.value.push(message)
            lastMessageId.value = Math.max(lastMessageId.value, message.id)
          }
        })
      }
    } catch (error) {
      console.error('Lỗi khi tải tin nhắn:', error)
    }
  }

  const sendMessage = async (content) => {
    try {
      const response = await chatAPI.sendMessage(content)
      if (response.data.success) {
        await fetchMessages()
        return { success: true }
      }
      return { success: false, message: response.data.message }
    } catch (error) {
      return { success: false, message: 'Gửi tin nhắn thất bại' }
    }
  }

  const fetchOnlineUsers = async () => {
    try {
      const response = await chatAPI.getOnlineUsers()
      if (response.data.users) {
        onlineUsers.value = response.data.users
      }
    } catch (error) {
      console.error('Lỗi khi tải danh sách online:', error)
    }
  }

  const updateLocation = async (location) => {
    try {
      await chatAPI.updateLocation(location)
    } catch (error) {
      console.error('Lỗi khi cập nhật vị trí:', error)
    }
  }
  
  const startPolling = () => {
    setInterval(() => {
      fetchMessages()
      fetchOnlineUsers()
    }, 2000)
  }
  
  return {
    messages,
    onlineUsers,
    lastMessageId,
    isScrolledToBottom,
    sortedMessages,
    fetchMessages,
    sendMessage,
    fetchOnlineUsers,
    updateLocation,
    startPolling
  }
})
