<template>
  <div class="admin-users">
    <n-card title="<PERSON><PERSON>ản lý người dùng">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchQuery"
            placeholder="<PERSON><PERSON><PERSON> kiếm người dùng..."
            clearable
            style="width: 200px;"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
          
          <n-button type="primary" @click="showAddUserModal = true">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
                </svg>
              </n-icon>
            </template>
            Thêm người dùng
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="filteredUsers"
        :pagination="pagination"
        :loading="loading"
        size="small"
      />
    </n-card>
    
    <n-modal v-model:show="showAddUserModal" preset="dialog" title="Thêm người dùng mới">
      <n-form ref="addUserFormRef" :model="newUser" :rules="addUserRules">
        <n-form-item path="username" label="Tên đăng nhập">
          <n-input v-model:value="newUser.username" placeholder="Nhập tên đăng nhập" />
        </n-form-item>
        
        <n-form-item path="email" label="Email">
          <n-input v-model:value="newUser.email" placeholder="Nhập email" />
        </n-form-item>
        
        <n-form-item path="password" label="Mật khẩu">
          <n-input v-model:value="newUser.password" type="password" placeholder="Nhập mật khẩu" />
        </n-form-item>
        
        <n-form-item path="is_admin" label="Vai trò">
          <n-select
            v-model:value="newUser.is_admin"
            :options="[
              { label: 'Người dùng thường', value: '0' },
              { label: 'Quản trị viên', value: '1' }
            ]"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showAddUserModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleAddUser" :loading="addingUser">
            Thêm
          </n-button>
        </n-space>
      </template>
    </n-modal>
    
    <n-modal v-model:show="showEditUserModal" preset="dialog" title="Chỉnh sửa người dùng">
      <n-form ref="editUserFormRef" :model="editingUser" :rules="editUserRules">
        <n-form-item path="username" label="Tên đăng nhập">
          <n-input v-model:value="editingUser.username" placeholder="Nhập tên đăng nhập" />
        </n-form-item>
        
        <n-form-item path="email" label="Email">
          <n-input v-model:value="editingUser.email" placeholder="Nhập email" />
        </n-form-item>
        
        <n-form-item path="signature" label="Chữ ký">
          <n-input v-model:value="editingUser.signature" placeholder="Nhập chữ ký" />
        </n-form-item>
        
        <n-form-item path="is_admin" label="Vai trò">
          <n-select
            v-model:value="editingUser.is_admin"
            :options="[
              { label: 'Người dùng thường', value: '0' },
              { label: 'Quản trị viên', value: '1' }
            ]"
          />
        </n-form-item>
        
        <n-form-item path="status" label="Trạng thái">
          <n-select
            v-model:value="editingUser.status"
            :options="[
              { label: 'Hoạt động', value: '1' },
              { label: 'Bị khóa', value: '0' }
            ]"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showEditUserModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleEditUser" :loading="editingUserLoading">
            Cập nhật
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, useMessage } from 'naive-ui'
import axios from 'axios'

const message = useMessage()

const users = ref([])
const loading = ref(false)
const searchQuery = ref('')

const showAddUserModal = ref(false)
const showEditUserModal = ref(false)
const addingUser = ref(false)
const editingUserLoading = ref(false)

const addUserFormRef = ref()
const editUserFormRef = ref()

const newUser = ref({
  username: '',
  email: '',
  password: '',
  is_admin: '0'
})

const editingUser = ref({})

const pagination = {
  pageSize: 10
}

const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  return users.value.filter(user => 
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const columns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 80,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'small',
      src: row.avatar || '/default-avatar.png'
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username'
  },
  {
    title: 'Email',
    key: 'email'
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    render: (row) => {
      return row.is_admin === '1' 
        ? h(NTag, { type: 'error', size: 'small' }, { default: () => 'Admin' })
        : h(NTag, { type: 'default', size: 'small' }, { default: () => 'User' })
    }
  },
  {
    title: 'Trạng thái',
    key: 'status',
    render: (row) => {
      return row.status === '1' 
        ? h(NTag, { type: 'success', size: 'small' }, { default: () => 'Hoạt động' })
        : h(NTag, { type: 'error', size: 'small' }, { default: () => 'Bị khóa' })
    }
  },
  {
    title: 'Đăng ký',
    key: 'created_at',
    render: (row) => new Date(row.created_at).toLocaleDateString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 200,
    render: (row) => h('div', { style: 'display: flex; gap: 8px;' }, [
      h(NButton, {
        size: 'small',
        onClick: () => handleEditUserClick(row)
      }, { default: () => 'Sửa' }),
      
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteUser(row.id)
      }, {
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error'
        }, { default: () => 'Xóa' }),
        default: () => 'Bạn có chắc muốn xóa người dùng này?'
      })
    ])
  }
]

const addUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' },
    { min: 6, message: 'Mật khẩu tối thiểu 6 ký tự', trigger: 'blur' }
  ]
}

const editUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ]
}

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/admin/users.php')
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách người dùng')
  } finally {
    loading.value = false
  }
}

const handleAddUser = async () => {
  try {
    await addUserFormRef.value?.validate()
    addingUser.value = true
    
    const response = await axios.post('/api/admin/users.php', newUser.value)
    
    if (response.data.success) {
      message.success('Thêm người dùng thành công')
      showAddUserModal.value = false
      newUser.value = { username: '', email: '', password: '', is_admin: '0' }
      fetchUsers()
    } else {
      message.error(response.data.message || 'Thêm người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingUser.value = false
  }
}

const handleEditUserClick = (user) => {
  editingUser.value = { ...user }
  showEditUserModal.value = true
}

const handleEditUser = async () => {
  try {
    await editUserFormRef.value?.validate()
    editingUserLoading.value = true
    
    const response = await axios.put(`/api/admin/users.php?id=${editingUser.value.id}`, editingUser.value)
    
    if (response.data.success) {
      message.success('Cập nhật người dùng thành công')
      showEditUserModal.value = false
      fetchUsers()
    } else {
      message.error(response.data.message || 'Cập nhật người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingUserLoading.value = false
  }
}

const handleDeleteUser = async (userId) => {
  try {
    const response = await axios.delete(`/api/admin/users.php?id=${userId}`)
    
    if (response.data.success) {
      message.success('Xóa người dùng thành công')
      fetchUsers()
    } else {
      message.error(response.data.message || 'Xóa người dùng thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa người dùng')
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users {
  max-width: 1200px;
}
</style>
