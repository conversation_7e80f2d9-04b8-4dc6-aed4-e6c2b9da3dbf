// @ts-nocheck
import{defineStore}from"pinia";import{ref,computed}from"vue";import{authAPI}from"../utils/api";export const useAuthStore=defineStore("auth",(()=>{const e=ref(JSON.parse(localStorage.getItem("user")||"null")),t=ref(localStorage.getItem("token"));console.log("Auth store initialized:"),console.log("- Token:",t.value),console.log("- User:",e.value);const s=computed((()=>{const e=!!t.value;return console.log("isAuthenticated computed:",e,"token:",t.value),e})),a=computed((()=>1===e.value?.is_admin||"1"===e.value?.is_admin)),r=()=>{e.value=null,t.value=null,localStorage.removeItem("token"),localStorage.removeItem("user")};return{user:e,token:t,isAuthenticated:s,isAdmin:a,login:async s=>{try{const a=await authAPI.login(s);return a.data.success?(t.value=a.data.token,e.value=a.data.user,localStorage.setItem("token",t.value),localStorage.setItem("user",JSON.stringify(a.data.user)),{success:!0,user:a.data.user}):{success:!1,message:a.data.message}}catch(e){return e.response&&401===e.response.status?{success:!1,message:"Tên đăng nhập hoặc mật khẩu không đúng."}:{success:!1,message:"Lỗi không xác định, vui lòng thử lại."}}},register:async e=>{try{return(await authAPI.register(e)).data}catch(e){return{success:!1,message:"Đăng ký thất bại"}}},logout:r,fetchUser:async()=>{try{const t=await authAPI.getUser();return t.data.success?(e.value=t.data.user,localStorage.setItem("user",JSON.stringify(t.data.user)),{success:!0}):{success:!1}}catch(e){return r(),{success:!1}}}}}));