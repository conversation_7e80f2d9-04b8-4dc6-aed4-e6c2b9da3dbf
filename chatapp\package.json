{"name": "chatapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vee-validate/rules": "^4.15.1", "axios": "^1.10.0", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "yup": "^1.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "vite": "^7.0.4"}}