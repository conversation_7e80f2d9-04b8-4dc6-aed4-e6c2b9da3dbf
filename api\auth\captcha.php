<?php
session_start();
header('Content-Type: image/png');

function generateCaptchaText($length = 5) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $captcha = '';
    for ($i = 0; $i < $length; $i++) {
        $captcha .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $captcha;
}

$captcha_text = generateCaptchaText();
$_SESSION['captcha'] = $captcha_text;

$width = 150;
$height = 50;
$image = imagecreate($width, $height);

$bg_color = imagecolorallocate($image, 30, 30, 30);
$text_color = imagecolorallocate($image, 255, 255, 255);
$line_color = imagecolorallocate($image, 100, 100, 100);
$noise_color = imagecolorallocate($image, 150, 150, 150);

imagefill($image, 0, 0, $bg_color);

for ($i = 0; $i < 5; $i++) {
    imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
}

for ($i = 0; $i < 100; $i++) {
    imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
}

$font_size = 16;
$angle = rand(-15, 15);
$x = 20;
$y = 35;

for ($i = 0; $i < strlen($captcha_text); $i++) {
    $char_angle = rand(-20, 20);
    $char_x = $x + ($i * 22);
    $char_y = $y + rand(-5, 5);
    
    imagestring($image, 5, $char_x, $char_y, $captcha_text[$i], $text_color);
}

imagepng($image);
imagedestroy($image);
?>
