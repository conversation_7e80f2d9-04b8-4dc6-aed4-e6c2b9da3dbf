// @ts-nocheck
import axios from"axios";const API_BASE_URL="",api=axios.create({baseURL:"",timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});api.interceptors.request.use((e=>{const a=localStorage.getItem("token");return console.log("API Request:",e.url,"Token:",a?"EXISTS":"MISSING"),a&&(e.headers.Authorization=`Bearer ${a}`),e}),(e=>Promise.reject(e))),api.interceptors.response.use((e=>e),(e=>(401===e.response?.status&&(console.log("401 Unauthorized - Token invalid"),localStorage.removeItem("token"),localStorage.removeItem("user")),Promise.reject(e))));export default api;export const authAPI={login:e=>api.post("/api/auth/login.php",e),register:e=>api.post("/api/auth/register.php",e),getUser:()=>api.get("/api/auth/user.php"),logout:()=>api.post("/api/auth/logout.php")};export const chatAPI={getMessages:(e=0)=>api.get(`/api/messages.php?last_id=${e}`),sendMessage:e=>api.post("/api/messages.php",{content:e}),getOnlineUsers:()=>api.get("/api/online.php"),updateLocation:e=>api.post("/api/update_location.php",{location:e})};export const adminAPI={getStats:()=>api.get("/api/admin/stats.php"),getActivities:()=>api.get("/api/admin/activities.php"),getUsers:()=>api.get("/api/admin/users.php"),createUser:e=>api.post("/api/admin/users.php",e),updateUser:(e,a)=>api.put(`/api/admin/users.php?id=${e}`,a),deleteUser:e=>api.delete(`/api/admin/users.php?id=${e}`),getMessages:()=>api.get("/api/admin/messages.php"),deleteMessage:e=>api.delete(`/api/admin/messages.php?id=${e}`),getBlacklist:()=>api.get("/api/admin/blacklist.php"),addToBlacklist:e=>api.post("/api/admin/blacklist.php",e),updateBlacklist:(e,a)=>api.put(`/api/admin/blacklist.php?id=${e}`,a),removeFromBlacklist:e=>api.delete(`/api/admin/blacklist.php?id=${e}`)};