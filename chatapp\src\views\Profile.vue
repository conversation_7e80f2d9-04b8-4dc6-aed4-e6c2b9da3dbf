<template>
  <div class="min-h-screen bg-gray-900 text-white">
    <!-- Header -->
    <div class="bg-gray-800 border-b border-gray-700 p-4">
      <div class="max-w-4xl mx-auto flex items-center justify-between">
        <h1 class="text-2xl font-bold"><PERSON><PERSON> sơ cá nhân</h1>
        <button 
          @click="$router.push('/chat')"
          class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors"
        >
          Quay lại Chat
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="max-w-4xl mx-auto p-6">
      <!-- User Info Card -->
      <div class="bg-gray-800 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Thông tin tài khoản</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Avatar -->
          <div class="text-center">
            <div class="w-32 h-32 bg-gray-700 rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </div>
            <button class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-sm transition-colors">
              Đổi avatar
            </button>
          </div>

          <!-- User Details -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1">Tên đăng nhập</label>
              <input 
                v-model="userInfo.username" 
                type="text" 
                readonly
                class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-gray-300 cursor-not-allowed"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1">Email</label>
              <input 
                v-model="userInfo.email" 
                type="email" 
                class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1">Nickname</label>
              <input 
                v-model="userInfo.nickname" 
                type="text" 
                class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Nhập nickname"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1">Chữ ký</label>
              <textarea 
                v-model="userInfo.signature" 
                rows="3"
                class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Nhập chữ ký của bạn"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-4 mt-6">
          <button 
            @click="updateProfile"
            :disabled="loading"
            class="bg-green-600 hover:bg-green-700 disabled:opacity-50 px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {{ loading ? 'Đang lưu...' : 'Cập nhật thông tin' }}
          </button>
          
          <button 
            @click="showChangePassword = true"
            class="bg-yellow-600 hover:bg-yellow-700 px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Đổi mật khẩu
          </button>
        </div>
      </div>

      <!-- Stats Card -->
      <div class="bg-gray-800 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Thống kê</h2>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-400">{{ stats.totalMessages }}</div>
            <div class="text-sm text-gray-400">Tin nhắn đã gửi</div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-green-400">{{ formatDate(userInfo.created_at) }}</div>
            <div class="text-sm text-gray-400">Ngày tham gia</div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-400">{{ formatDate(userInfo.last_login) }}</div>
            <div class="text-sm text-gray-400">Lần cuối đăng nhập</div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-yellow-400">{{ userInfo.is_admin ? 'Admin' : 'User' }}</div>
            <div class="text-sm text-gray-400">Vai trò</div>
          </div>
        </div>
      </div>

      <!-- Security Info -->
      <div class="bg-gray-800 rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Thông tin bảo mật</h2>
        
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-300">IP hiện tại:</span>
            <span class="font-mono text-sm bg-gray-700 px-2 py-1 rounded">{{ userInfo.ip || 'N/A' }}</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-gray-300">Token:</span>
            <span class="font-mono text-xs bg-gray-700 px-2 py-1 rounded max-w-xs truncate">{{ userInfo.token || 'N/A' }}</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-gray-300">Trạng thái:</span>
            <span class="px-2 py-1 rounded text-sm" :class="userInfo.status ? 'bg-green-600' : 'bg-red-600'">
              {{ userInfo.status ? 'Hoạt động' : 'Bị khóa' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div v-if="showChangePassword" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">Đổi mật khẩu</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Mật khẩu hiện tại</label>
            <input 
              v-model="passwordForm.currentPassword" 
              type="password" 
              class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Mật khẩu mới</label>
            <input 
              v-model="passwordForm.newPassword" 
              type="password" 
              class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Xác nhận mật khẩu mới</label>
            <input 
              v-model="passwordForm.confirmPassword" 
              type="password" 
              class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>

        <div v-if="passwordMessage" class="mt-4 p-3 rounded-lg" :class="passwordMessage.type === 'success' ? 'bg-green-600' : 'bg-red-600'">
          <p class="text-sm">{{ passwordMessage.text }}</p>
        </div>

        <div class="flex gap-3 mt-6">
          <button 
            @click="changePassword"
            :disabled="passwordLoading"
            class="flex-1 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 py-2 rounded-lg font-medium transition-colors"
          >
            {{ passwordLoading ? 'Đang xử lý...' : 'Đổi mật khẩu' }}
          </button>
          
          <button 
            @click="showChangePassword = false"
            class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg font-medium transition-colors"
          >
            Hủy
          </button>
        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="message" class="fixed top-4 right-4 p-4 rounded-lg z-50" :class="message.type === 'success' ? 'bg-green-600' : 'bg-red-600'">
      <p class="text-white">{{ message.text }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'

const authStore = useAuthStore()

const loading = ref(false)
const showChangePassword = ref(false)
const passwordLoading = ref(false)
const message = ref(null)
const passwordMessage = ref(null)

const userInfo = reactive({
  username: '',
  email: '',
  nickname: '',
  signature: '',
  ip: '',
  token: '',
  status: 1,
  is_admin: 0,
  created_at: '',
  last_login: ''
})

const stats = reactive({
  totalMessages: 0
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('vi-VN')
}

const loadUserInfo = async () => {
  try {
    const user = authStore.user
    if (user) {
      Object.assign(userInfo, user)
    }
  } catch (error) {
    console.error('Error loading user info:', error)
  }
}

const updateProfile = async () => {
  loading.value = true
  message.value = null
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.value = {
      type: 'success',
      text: 'Cập nhật thông tin thành công!'
    }
    
    setTimeout(() => {
      message.value = null
    }, 3000)
  } catch (error) {
    message.value = {
      type: 'error',
      text: 'Có lỗi xảy ra khi cập nhật thông tin'
    }
  } finally {
    loading.value = false
  }
}

const changePassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordMessage.value = {
      type: 'error',
      text: 'Mật khẩu xác nhận không khớp'
    }
    return
  }
  
  if (passwordForm.newPassword.length < 6) {
    passwordMessage.value = {
      type: 'error',
      text: 'Mật khẩu mới phải có ít nhất 6 ký tự'
    }
    return
  }
  
  passwordLoading.value = true
  passwordMessage.value = null
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    passwordMessage.value = {
      type: 'success',
      text: 'Đổi mật khẩu thành công!'
    }
    
    setTimeout(() => {
      showChangePassword.value = false
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      passwordMessage.value = null
    }, 2000)
  } catch (error) {
    passwordMessage.value = {
      type: 'error',
      text: 'Có lỗi xảy ra khi đổi mật khẩu'
    }
  } finally {
    passwordLoading.value = false
  }
}

onMounted(() => {
  loadUserInfo()
})
</script>
