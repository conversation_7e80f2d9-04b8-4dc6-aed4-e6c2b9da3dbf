<template>
  <div class="chat-layout">
    <n-layout has-sider>
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="0"
        :width="320"
        :collapsed="collapsed"
        show-trigger
        @collapse="collapsed = true"
        @expand="collapsed = false"
      >
        <div class="sidebar-content">
          <div class="user-profile">
            <n-avatar
              round
              size="large"
              :src="authStore.user?.avatar || '/default-avatar.png'"
            />
            <div class="user-info">
              <h3>{{ authStore.user?.username }}</h3>
              <n-tag v-if="authStore.isAdmin" type="error" size="small">
                <template #icon>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C14.8,12.4 14.4,13.2 13.7,13.7V16.5C13.7,17.3 13.1,18 12.3,18H11.7C10.9,18 10.3,17.3 10.3,16.5V13.8C9.6,13.3 9.2,12.5 9.2,11.6V10C9.2,8.6 10.6,7 12,7Z"/>
                    </svg>
                  </n-icon>
                </template>
                Quản trị viên
              </n-tag>
              <p>{{ authStore.user?.signature || 'Chưa có chữ ký' }}</p>
            </div>
          </div>
          
          <n-divider />
          
          <div class="online-users">
            <h4>Người dùng online ({{ chatStore.onlineUsers.length }})</h4>
            <n-list>
              <n-list-item v-for="user in chatStore.onlineUsers" :key="user.id">
                <n-thing>
                  <template #avatar>
                    <n-avatar
                      round
                      size="small"
                      :src="user.avatar || '/default-avatar.png'"
                    />
                  </template>
                  <template #header>
                    {{ user.username }}
                    <n-tag v-if="user.is_admin === '1'" type="error" size="tiny">
                      Admin
                    </n-tag>
                  </template>
                  <template #description>
                    <n-text depth="3" style="font-size: 12px">
                      {{ user.location || 'Không xác định' }}
                    </n-text>
                  </template>
                </n-thing>
              </n-list-item>
            </n-list>
          </div>
          
          <n-divider />
          
          <div class="sidebar-actions">
            <n-button-group vertical>
              <n-button @click="$router.push('/profile')" ghost>
                <template #icon>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                    </svg>
                  </n-icon>
                </template>
                Hồ sơ
              </n-button>
              
              <n-button v-if="authStore.isAdmin" @click="$router.push('/adminrmgihbvS6')" ghost>
                <template #icon>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                  </n-icon>
                </template>
                Quản trị
              </n-button>
              
              <n-button @click="handleLogout" ghost type="error">
                <template #icon>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/>
                    </svg>
                  </n-icon>
                </template>
                Đăng xuất
              </n-button>
            </n-button-group>
          </div>
        </div>
      </n-layout-sider>
      
      <n-layout>
        <n-layout-header bordered style="height: 64px; padding: 0 24px; display: flex; align-items: center;">
          <h2>TUNGDUONGCMS Chat</h2>
          <div style="flex: 1"></div>
          <n-space>
            <n-badge :value="chatStore.onlineUsers.length" type="success">
              <n-button circle quaternary>
                <template #icon>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4M10.5 12.5c.28 0 .5.22.5.5s-.22.5-.5.5s-.5-.22-.5-.5s.22-.5.5-.5M15 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M12.5 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M10 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1"/>
                    </svg>
                  </n-icon>
                </template>
              </n-button>
            </n-badge>
          </n-space>
        </n-layout-header>
        
        <n-layout-content style="padding: 0;">
          <div class="chat-container">
            <div ref="messagesContainer" class="messages-container">
              <div v-for="message in chatStore.sortedMessages" :key="message.id" class="message-wrapper">
                <ChatMessage :message="message" :is-own="message.user_id == authStore.user?.id" />
              </div>
            </div>
            
            <div class="message-input-container">
              <n-space>
                <n-button circle @click="showEmojiPicker = !showEmojiPicker">
                  <template #icon>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12,2C6.486,2,2,6.486,2,12s4.486,10,10,10s10-4.486,10-10S17.514,2,12,2z M12,20c-4.411,0-8-3.589-8-8 s3.589-8,8-8s8,3.589,8,8S16.411,20,12,20z"/>
                        <circle fill="currentColor" cx="8.5" cy="10.5" r="1.5"/>
                        <circle fill="currentColor" cx="15.5" cy="10.5" r="1.5"/>
                        <path fill="currentColor" d="M12,18c4,0,5-4,5-4H7S8,18,12,18z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-button>
                
                <n-input
                  v-model:value="messageText"
                  type="textarea"
                  placeholder="Nhập tin nhắn..."
                  :autosize="{ minRows: 1, maxRows: 4 }"
                  @keydown="handleKeyDown"
                  style="flex: 1"
                />
                
                <n-button type="primary" @click="sendMessage" :loading="sending">
                  <template #icon>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                      </svg>
                    </n-icon>
                  </template>
                  Gửi
                </n-button>
              </n-space>
              
              <div v-if="showEmojiPicker" class="emoji-picker">
                <n-grid :cols="8" :x-gap="8" :y-gap="8">
                  <n-grid-item v-for="emoji in emojis" :key="emoji" @click="addEmoji(emoji)">
                    <n-button text style="font-size: 20px;">{{ emoji }}</n-button>
                  </n-grid-item>
                </n-grid>
              </div>
            </div>
          </div>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '../stores/auth'
import { useChatStore } from '../stores/chat'
import ChatMessage from '../components/ChatMessage.vue'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()
const chatStore = useChatStore()

const collapsed = ref(false)
const messageText = ref('')
const sending = ref(false)
const showEmojiPicker = ref(false)
const messagesContainer = ref()

const emojis = ['😊', '😂', '🤣', '😍', '🥰', '😘', '😅', '😁', '🤔', '🤗', '😉', '😎', '😡', '😭', '😱', '👍', '👎', '❤️', '💔', '🎉']

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const sendMessage = async () => {
  if (!messageText.value.trim()) return
  
  sending.value = true
  const result = await chatStore.sendMessage(messageText.value)
  
  if (result.success) {
    messageText.value = ''
    showEmojiPicker.value = false
    await nextTick()
    scrollToBottom()
  } else {
    message.error(result.message || 'Gửi tin nhắn thất bại')
  }
  
  sending.value = false
}

const addEmoji = (emoji) => {
  messageText.value += emoji
  showEmojiPicker.value = false
}

const handleKeyDown = (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    sendMessage()
  }
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

watch(() => chatStore.sortedMessages.length, () => {
  nextTick(() => {
    if (chatStore.isScrolledToBottom) {
      scrollToBottom()
    }
  })
})

onMounted(() => {
  chatStore.fetchMessages()
  chatStore.fetchOnlineUsers()
  chatStore.startPolling()
  
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition((position) => {
      const { latitude, longitude } = position.coords
      chatStore.updateLocation(`${latitude},${longitude}`)
    })
  }
})
</script>

<style scoped>
.chat-layout {
  height: 100vh;
}

.sidebar-content {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.user-info h3 {
  margin: 0;
  font-size: 16px;
}

.user-info p {
  margin: 4px 0 0;
  font-size: 12px;
  color: #666;
}

.online-users {
  flex: 1;
  overflow-y: auto;
}

.online-users h4 {
  margin: 0 0 12px;
  font-size: 14px;
  color: #333;
}

.sidebar-actions {
  margin-top: auto;
}

.chat-container {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
}

.message-wrapper {
  margin-bottom: 16px;
}

.message-input-container {
  padding: 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
  position: relative;
}

.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 320px;
}
</style>
