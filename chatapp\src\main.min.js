// @ts-nocheck
import{createApp}from"vue";import{createPinia}from"pinia";import{createRouter,createWebHistory}from"vue-router";import naive from"naive-ui";import App from"./App.vue";import routes from"./router/index.js";import{useAuthStore}from"./stores/auth.js";import"./style.css";const router=createRouter({history:createWebHistory(),routes:routes}),pinia=createPinia(),app=createApp(App);app.use(pinia),app.use(router),app.use(naive),router.beforeEach((async(e,o,t)=>{const r=useAuthStore();if(console.log("=== ROUTER GUARD DEBUG ==="),console.log("Going to:",e.path),console.log("Token from localStorage:",localStorage.getItem("token")),console.log("User from localStorage:",localStorage.getItem("user")),console.log("Auth store token:",r.token),console.log("Auth store user:",r.user),console.log("Is authenticated:",r.isAuthenticated),r.token&&!r.user){console.log("Fetching user data...");try{const e=await r.fetchUser();console.log("Fetch user result:",e)}catch(e){console.error("Failed to fetch user:",e),r.logout()}}const s=e.matched.some((e=>e.meta.requiresAuth)),a=e.matched.some((e=>e.meta.requiresGuest)),i=e.matched.some((e=>e.meta.requiresAdmin));console.log("Route requires auth:",s),console.log("Route requires guest:",a),console.log("Route requires admin:",i),console.log("Final auth check:",r.isAuthenticated),s&&!r.isAuthenticated?(console.log("Redirecting to login - not authenticated"),t("/login")):a&&r.isAuthenticated?(console.log("Redirecting to chat - already authenticated"),t("/chat")):i&&!r.isAdmin?(console.log("Redirecting to chat - not admin"),t("/chat")):(console.log("Allowing navigation"),t()),console.log("=== END ROUTER GUARD ===")})),app.mount("#app");