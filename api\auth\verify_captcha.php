<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['captcha']) || !isset($_SESSION['captcha'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Captcha không hợp lệ']);
    exit;
}

$user_captcha = strtoupper(trim($input['captcha']));
$session_captcha = strtoupper($_SESSION['captcha']);

if ($user_captcha === $session_captcha) {
    echo json_encode(['success' => true, 'message' => 'Captcha đúng']);
} else {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Captcha không đúng']);
}

unset($_SESSION['captcha']);
?>
