// @ts-nocheck
import Login from"../views/auth/Login.vue";import Register from"../views/auth/Register.vue";import Chat from"../views/Chat.vue";import Profile from"../views/Profile.vue";import AdminLayout from"../views/admin/AdminLayout.vue";import AdminDashboard from"../views/admin/Dashboard.vue";import AdminUsers from"../views/admin/Users.vue";import AdminMessages from"../views/admin/Messages.vue";import AdminBlacklist from"../views/admin/Blacklist.vue";const routes=[{path:"/",redirect:"/chat"},{path:"/login",name:"Login",component:Login,meta:{requiresGuest:!0}},{path:"/register",name:"Register",component:Register,meta:{requiresGuest:!0}},{path:"/chat",name:"Chat",component:Chat,meta:{requiresAuth:!0}},{path:"/profile",name:"Profile",component:Profile,meta:{requiresAuth:!0}},{path:"/adminrmgihbvS6",component:AdminLayout,meta:{requiresAuth:!0,requiresAdmin:!0},children:[{path:"",name:"AdminDashboard",component:AdminDashboard},{path:"users",name:"AdminUsers",component:AdminUsers},{path:"messages",name:"AdminMessages",component:AdminMessages},{path:"blacklist",name:"AdminBlacklist",component:AdminBlacklist}]}];export default routes;