<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

$num1 = rand(1, 10);
$num2 = rand(1, 10);
$operators = ['+', '-'];
$operator = $operators[array_rand($operators)];

if ($operator === '+') {
    $answer = $num1 + $num2;
    $question = "$num1 + $num2";
} else {
    if ($num1 < $num2) {
        $temp = $num1;
        $num1 = $num2;
        $num2 = $temp;
    }
    $answer = $num1 - $num2;
    $question = "$num1 - $num2";
}

$_SESSION['captcha'] = $answer;

jsonResponse([
    'success' => true,
    'question' => $question
]);
?>
